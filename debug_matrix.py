#!/usr/bin/env python3
"""
Debug script to see the expanded matrix for the complex table
"""

import sys
import os

# Add current directory to path to import finalcode2
sys.path.insert(0, os.getcwd())

from finalcode2 import process_tables, build_expanded_matrix, classify_table_from_data

def debug_complex_table():
    """Debug the complex table to see the matrix structure"""
    html_input = """
    <table>
        <tr>
            <th colspan="2">Product Information</th>
            <th>Price</th>
            <th>Stock</th>
        </tr>
        <tr>
            <th>Name</th>
            <th>Category</th>
            <th>USD</th>
            <th>Units</th>
        </tr>
        <tr>
            <td>Apple</td>
            <td>Fruit</td>
            <td>$1.00</td>
            <td>100</td>
        </tr>
        <tr>
            <td>Banana</td>
            <td>Fruit</td>
            <td>$0.50</td>
            <td>200</td>
        </tr>
    </table>
    """
    
    print("=== Debugging Complex Table Matrix ===")
    
    # Process the table to get the raw data
    tables = process_tables(html_input)
    if tables:
        table = tables[0]
        print("Raw table data:")
        print("with_span structure:")
        for i, row in enumerate(table.get('with_span', [])):
            print(f"  Row {i}: {row}")
        
        print("\nExpanded matrix:")
        matrix = build_expanded_matrix(table)
        for i, row in enumerate(matrix):
            print(f"  Row {i}: {row}")
        
        print(f"\nClassification: {classify_table_from_data(table)}")
        
        # Check empty ratio
        total_cells = sum(len(row) for row in matrix)
        empty_cells = sum(1 for row in matrix for cell in row if not cell.strip())
        empty_ratio = empty_cells / total_cells if total_cells > 0 else 0
        print(f"Empty ratio: {empty_ratio:.2f}")

if __name__ == "__main__":
    debug_complex_table()
