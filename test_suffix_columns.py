#!/usr/bin/env python3
"""
Test script to check suffix column combining behavior
"""

import sys
import os

# Add current directory to path to import finalcode2
sys.path.insert(0, os.getcwd())

from finalcode2 import combine_suffix_columns, has_suffix_columns

def test_suffix_column_combining():
    """Test suffix column combining functionality"""
    
    print("=== Testing Suffix Column Combining ===")
    
    # Test case 1: Simple suffix columns
    test_data_1 = {
        "Name": "John",
        "Name_2": "Smith", 
        "Age": "25",
        "City": "New York"
    }
    
    print("Test 1 - Simple suffix columns:")
    print("Input:", test_data_1)
    print("Has suffix columns:", has_suffix_columns(test_data_1))
    result_1 = combine_suffix_columns(test_data_1)
    print("Output:", result_1)
    print()
    
    # Test case 2: Multiple suffix columns
    test_data_2 = {
        "Product": "Apple",
        "Product_2": "iPhone",
        "Product_3": "MacBook",
        "Price": "$999",
        "Price_2": "$1299"
    }
    
    print("Test 2 - Multiple suffix columns:")
    print("Input:", test_data_2)
    print("Has suffix columns:", has_suffix_columns(test_data_2))
    result_2 = combine_suffix_columns(test_data_2)
    print("Output:", result_2)
    print()
    
    # Test case 3: Suffix columns with empty values
    test_data_3 = {
        "Name": "John",
        "Name_2": "",
        "Name_3": "Doe",
        "Age": "25",
        "Notes": "",
        "Notes_2": "Important"
    }
    
    print("Test 3 - Suffix columns with empty values:")
    print("Input:", test_data_3)
    print("Has suffix columns:", has_suffix_columns(test_data_3))
    result_3 = combine_suffix_columns(test_data_3)
    print("Output:", result_3)
    print()
    
    # Test case 4: Orphaned suffix columns (no base column)
    test_data_4 = {
        "Name_2": "Smith",
        "Name_3": "Jr",
        "Age": "25",
        "Title_2": "Manager"
    }
    
    print("Test 4 - Orphaned suffix columns:")
    print("Input:", test_data_4)
    print("Has suffix columns:", has_suffix_columns(test_data_4))
    result_4 = combine_suffix_columns(test_data_4)
    print("Output:", result_4)
    print()
    
    # Test case 5: List of dictionaries with suffix columns
    test_data_5 = [
        {
            "Name": "John",
            "Name_2": "Smith",
            "Age": "25"
        },
        {
            "Name": "Jane",
            "Name_2": "Doe", 
            "Age": "30"
        }
    ]
    
    print("Test 5 - List of dictionaries with suffix columns:")
    print("Input:", test_data_5)
    print("Has suffix columns:", has_suffix_columns(test_data_5))
    result_5 = combine_suffix_columns(test_data_5)
    print("Output:", result_5)
    print()

if __name__ == "__main__":
    test_suffix_column_combining()
