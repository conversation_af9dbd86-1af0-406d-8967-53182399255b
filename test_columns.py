#!/usr/bin/env python3
"""
Test script to identify column disappearing issue in finalcode2.py
"""

import sys
import os

# Add current directory to path to import finalcode2
sys.path.insert(0, os.getcwd())

from finalcode2 import correct_tables

def test_simple_table():
    """Test a simple table to see if columns disappear"""
    html_input = """
    <table>
        <tr>
            <th>Name</th>
            <th>Age</th>
            <th>City</th>
            <th>Country</th>
        </tr>
        <tr>
            <td>John</td>
            <td>25</td>
            <td>New York</td>
            <td>USA</td>
        </tr>
        <tr>
            <td>Jane</td>
            <td>30</td>
            <td>London</td>
            <td>UK</td>
        </tr>
    </table>
    """
    
    print("=== Testing Simple Table ===")
    print("Input HTML:")
    print(html_input.strip())
    print("\nOutput:")
    result = correct_tables(html_input)
    print(result)
    print("\n" + "="*50 + "\n")

def test_table_with_empty_cells():
    """Test a table with some empty cells"""
    html_input = """
    <table>
        <tr>
            <th>Product</th>
            <th>Price</th>
            <th>Stock</th>
            <th>Notes</th>
        </tr>
        <tr>
            <td>Apple</td>
            <td>$1.00</td>
            <td>100</td>
            <td></td>
        </tr>
        <tr>
            <td>Banana</td>
            <td>$0.50</td>
            <td></td>
            <td>Out of stock</td>
        </tr>
        <tr>
            <td>Orange</td>
            <td></td>
            <td>50</td>
            <td>Price pending</td>
        </tr>
    </table>
    """
    
    print("=== Testing Table with Empty Cells ===")
    print("Input HTML:")
    print(html_input.strip())
    print("\nOutput:")
    result = correct_tables(html_input)
    print(result)
    print("\n" + "="*50 + "\n")

def test_table_with_empty_column():
    """Test a table with an entirely empty column"""
    html_input = """
    <table>
        <tr>
            <th>Name</th>
            <th>Age</th>
            <th>Empty Column</th>
            <th>City</th>
        </tr>
        <tr>
            <td>John</td>
            <td>25</td>
            <td></td>
            <td>New York</td>
        </tr>
        <tr>
            <td>Jane</td>
            <td>30</td>
            <td></td>
            <td>London</td>
        </tr>
    </table>
    """
    
    print("=== Testing Table with Empty Column ===")
    print("Input HTML:")
    print(html_input.strip())
    print("\nOutput:")
    result = correct_tables(html_input)
    print(result)
    print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    test_simple_table()
    test_table_with_empty_cells()
    test_table_with_empty_column()
