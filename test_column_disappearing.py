#!/usr/bin/env python3
"""
Test script to identify specific cases where columns disappear
"""

import sys
import os

# Add current directory to path to import finalcode2
sys.path.insert(0, os.getcwd())

from finalcode2 import correct_tables

def test_complex_table():
    """Test a more complex table that might trigger column disappearing"""
    html_input = """
    <table>
        <tr>
            <th colspan="2">Product Information</th>
            <th>Price</th>
            <th>Stock</th>
        </tr>
        <tr>
            <th>Name</th>
            <th>Category</th>
            <th>USD</th>
            <th>Units</th>
        </tr>
        <tr>
            <td>Apple</td>
            <td>Fruit</td>
            <td>$1.00</td>
            <td>100</td>
        </tr>
        <tr>
            <td>Banana</td>
            <td>Fruit</td>
            <td>$0.50</td>
            <td>200</td>
        </tr>
    </table>
    """
    
    print("=== Testing Complex Table with Colspan ===")
    print("Input HTML:")
    print(html_input.strip())
    print("\nOutput:")
    result = correct_tables(html_input)
    print(result)
    print("\n" + "="*50 + "\n")

def test_table_with_many_empty_cells():
    """Test a table with many empty cells that might trigger different processing"""
    html_input = """
    <table>
        <tr>
            <th>Item</th>
            <th>Q1</th>
            <th>Q2</th>
            <th>Q3</th>
            <th>Q4</th>
            <th>Total</th>
        </tr>
        <tr>
            <td>Revenue</td>
            <td>100</td>
            <td></td>
            <td>150</td>
            <td></td>
            <td>250</td>
        </tr>
        <tr>
            <td>Expenses</td>
            <td></td>
            <td>50</td>
            <td></td>
            <td>75</td>
            <td>125</td>
        </tr>
        <tr>
            <td>Profit</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td>125</td>
        </tr>
    </table>
    """
    
    print("=== Testing Table with Many Empty Cells ===")
    print("Input HTML:")
    print(html_input.strip())
    print("\nOutput:")
    result = correct_tables(html_input)
    print(result)
    print("\n" + "="*50 + "\n")

def test_table_with_rowspan():
    """Test a table with rowspan that might cause issues"""
    html_input = """
    <table>
        <tr>
            <th>Category</th>
            <th>Item</th>
            <th>Price</th>
            <th>Notes</th>
        </tr>
        <tr>
            <td rowspan="2">Fruits</td>
            <td>Apple</td>
            <td>$1.00</td>
            <td>Fresh</td>
        </tr>
        <tr>
            <td>Banana</td>
            <td>$0.50</td>
            <td></td>
        </tr>
        <tr>
            <td rowspan="2">Vegetables</td>
            <td>Carrot</td>
            <td>$0.75</td>
            <td>Organic</td>
        </tr>
        <tr>
            <td>Lettuce</td>
            <td>$1.25</td>
            <td></td>
        </tr>
    </table>
    """
    
    print("=== Testing Table with Rowspan ===")
    print("Input HTML:")
    print(html_input.strip())
    print("\nOutput:")
    result = correct_tables(html_input)
    print(result)
    print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    test_complex_table()
    test_table_with_many_empty_cells()
    test_table_with_rowspan()
