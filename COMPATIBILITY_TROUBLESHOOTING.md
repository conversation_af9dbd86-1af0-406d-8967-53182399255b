# finalcode2.py Compatibility Troubleshooting Guide

## 🚨 Common Problems & Solutions

### Problem 1: Unicode Symbols Not Detected/Processed

**Symptoms:**
- Code works on your laptop but not colleague's
- Symbols appear as question marks (?) or squares (□)
- No symbol replacement occurs

**Root Causes:**
1. **Encoding Issues**: File saved with different encoding
2. **Locale Settings**: Different system locale/code page
3. **Python Environment**: Different Python versions or settings
4. **Font Issues**: Symbols display but aren't properly encoded

**Solutions:**

#### Quick Fix (Run on colleague's laptop):
```bash
# 1. Set environment variables
set PYTHONIOENCODING=utf-8
set LANG=en_US.UTF-8

# 2. Run diagnostic
python diagnose_compatibility.py

# 3. Run compatibility fixer
python fix_compatibility_issues.py
```

#### Detailed Solutions:

**A. File Encoding Issues:**
```python
# Save file with explicit UTF-8 encoding
with open('test_symbols.txt', 'w', encoding='utf-8-sig') as f:
    f.write(content)

# Or read with multiple encoding attempts
encodings = ['utf-8-sig', 'utf-8', 'utf-16', 'cp1252']
for encoding in encodings:
    try:
        with open('file.txt', 'r', encoding=encoding) as f:
            content = f.read()
        break
    except UnicodeDecodeError:
        continue
```

**B. System Locale Issues:**
```python
import locale
import os

# Windows
os.system('chcp 65001')  # Set to UTF-8 code page
locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')

# Linux/Mac
os.environ['LC_ALL'] = 'en_US.UTF-8'
os.environ['LANG'] = 'en_US.UTF-8'
```

**C. Python Environment:**
```python
import sys
print(f"Python version: {sys.version}")
print(f"Default encoding: {sys.getdefaultencoding()}")
print(f"File system encoding: {sys.getfilesystemencoding()}")

# Should show UTF-8 for proper Unicode support
```

### Problem 2: Symbol Corruption During Transfer

**Symptoms:**
- Symbols become HTML entities (&check;, &#9744;)
- Symbols become letters (x, o, X, O)
- Symbols become byte sequences (â˜, âœ")

**Solutions:**

```python
# Symbol normalization function
def normalize_symbols(content):
    fixes = {
        '&check;': '✓',
        '&cross;': '❌', 
        '&#9744;': '☐',
        '&#9746;': '☒',
        '?': '☐',  # Common corruption
        'x': '❌',
        'X': '❌',
        'o': '☐',
        'O': '☐'
    }
    
    for corrupted, correct in fixes.items():
        content = content.replace(corrupted, correct)
    
    return content
```

### Problem 3: Different Python Versions

**Symptoms:**
- ImportError or ModuleNotFoundError
- Different Unicode handling behavior
- Encoding-related exceptions

**Solutions:**

1. **Check Python Version:**
```bash
python --version
# Should be 3.8+ for best Unicode support
```

2. **Install Required Modules:**
```bash
pip install unicodedata  # Usually built-in
```

3. **Version-Specific Fixes:**
```python
import sys
if sys.version_info < (3, 8):
    print("⚠️ Python 3.8+ recommended for full Unicode support")
```

### Problem 4: Windows vs Linux/Mac Differences

**Common Issues:**
- Different default encodings (cp1252 vs utf-8)
- Different line endings (\r\n vs \n)
- Different locale settings

**Solutions:**

```python
import sys
import os

if sys.platform == 'win32':
    # Windows-specific fixes
    os.system('chcp 65001')  # UTF-8 code page
    os.environ['PYTHONIOENCODING'] = 'utf-8'
else:
    # Unix-like systems
    os.environ['LC_ALL'] = 'en_US.UTF-8'
    os.environ['LANG'] = 'en_US.UTF-8'
```

## 🔧 Diagnostic Tools

### 1. Run System Diagnosis:
```bash
python diagnose_compatibility.py test/test_symbol.txt
```

### 2. Check Symbol Content:
```bash
python debug_symbols_detailed.py
```

### 3. Test Processing Pipeline:
```python
import finalcode2

# Test symbol detection
content = "Test: ✓ ❌ ☐ ☒"
symbols = finalcode2.detect_unicode_symbols(content)
print(f"Detected: {len(symbols)} symbols")

# Test replacement
replaced = finalcode2.replace_symbols(content)
print(f"Replaced: {replaced}")
```

## 📋 Deployment Checklist

**For Colleague's Laptop:**

1. ✅ **Copy Files:**
   - finalcode2.py
   - diagnose_compatibility.py
   - fix_compatibility_issues.py
   - test/test_symbol.txt

2. ✅ **Run Diagnostics:**
   ```bash
   python diagnose_compatibility.py
   ```

3. ✅ **Apply Fixes:**
   ```bash
   python fix_compatibility_issues.py
   ```

4. ✅ **Test Processing:**
   ```bash
   python finalcode2.py
   ```

5. ✅ **Compare Results:**
   - Both laptops should produce identical output
   - Check all_tables_converted.txt file

## 🆘 Emergency Fallback

If all else fails, use this minimal working version:

```python
import sys
import os

# Force UTF-8 environment
os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout.reconfigure(encoding='utf-8')

# Simple symbol replacement
def emergency_symbol_fix(content):
    replacements = {
        '✓': 'Yes', '❌': 'No', '☐': 'No', '☒': 'Yes',
        '?': 'Unknown', 'x': 'No', 'X': 'No', 'o': 'No'
    }
    
    for symbol, replacement in replacements.items():
        content = content.replace(symbol, replacement)
    
    return content

# Use before processing
content = emergency_symbol_fix(your_content)
result = finalcode2.correct_tables(content)
```

## 📞 Support

If issues persist:
1. Run diagnostics on both laptops
2. Compare Python versions and locale settings  
3. Check if symbols are visible in text editors
4. Test with simple symbol strings first
5. Consider using UTF-8 BOM encoding for files
